package activity_cashback

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestAccumulatedTradingVolumeTaskIdentification tests the task identification logic
func TestAccumulatedTradingVolumeTaskIdentification(t *testing.T) {
	// Create task management service for testing identification logic
	service := &TaskManagementService{}

	t.Run("IsAccumulatedTradingVolumeTask_ValidTask_ReturnsTrue", func(t *testing.T) {
		task := &model.ActivityTask{
			ID:             uuid.New(),
			Name:           "Accumulated MEME Trading $1000",
			TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIdentifier("ACCUMULATED_MEME_TRADING_1000"); return &id }(),
			ActionTarget:   func() *string { s := "memeTrade"; return &s }(),
			Frequency:      model.FrequencyProgressive,
			Conditions: &model.TaskConditions{
				MinTradingVolume: func() *float64 { v := 1000.0; return &v }(),
			},
			Points: 100,
		}

		result := service.IsAccumulatedTradingVolumeTask(task)
		assert.True(t, result)
	})

	t.Run("IsAccumulatedTradingVolumeTask_RegularTask_ReturnsFalse", func(t *testing.T) {
		regularTask := &model.ActivityTask{
			ID:           uuid.New(),
			Name:         "Daily Check-in",
			ActionTarget: func() *string { s := "checkin"; return &s }(),
			Frequency:    model.FrequencyDaily,
		}
		result := service.IsAccumulatedTradingVolumeTask(regularTask)
		assert.False(t, result)
	})
}

// TestTaskIdentifierPatterns tests the task identifier pattern matching
func TestTaskIdentifierPatterns(t *testing.T) {
	service := &TaskManagementService{}

	testCases := []struct {
		name        string
		task        *model.ActivityTask
		expected    bool
		description string
	}{
		{
			name: "ValidAccumulatedTradingTask",
			task: &model.ActivityTask{
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIdentifier("ACCUMULATED_MEME_TRADING_1000"); return &id }(),
				ActionTarget:   func() *string { s := "memeTrade"; return &s }(),
				Frequency:      model.FrequencyProgressive,
				Conditions: &model.TaskConditions{
					MinTradingVolume: func() *float64 { v := 1000.0; return &v }(),
				},
			},
			expected:    true,
			description: "Should identify accumulated trading task with correct pattern",
		},
		{
			name: "ValidAccumulatedTradingTaskDifferentAmount",
			task: &model.ActivityTask{
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIdentifier("ACCUMULATED_MEME_TRADING_5000"); return &id }(),
				ActionTarget:   func() *string { s := "memeTrade"; return &s }(),
				Frequency:      model.FrequencyProgressive,
				Conditions: &model.TaskConditions{
					MinTradingVolume: func() *float64 { v := 5000.0; return &v }(),
				},
			},
			expected:    true,
			description: "Should identify accumulated trading task with different amount",
		},
		{
			name: "InvalidActionTarget",
			task: &model.ActivityTask{
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIdentifier("ACCUMULATED_MEME_TRADING_1000"); return &id }(),
				ActionTarget:   func() *string { s := "checkin"; return &s }(),
				Frequency:      model.FrequencyProgressive,
				Conditions: &model.TaskConditions{
					MinTradingVolume: func() *float64 { v := 1000.0; return &v }(),
				},
			},
			expected:    false,
			description: "Should not identify task with wrong action target",
		},
		{
			name: "InvalidFrequency",
			task: &model.ActivityTask{
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIdentifier("ACCUMULATED_MEME_TRADING_1000"); return &id }(),
				ActionTarget:   func() *string { s := "memeTrade"; return &s }(),
				Frequency:      model.FrequencyDaily,
				Conditions: &model.TaskConditions{
					MinTradingVolume: func() *float64 { v := 1000.0; return &v }(),
				},
			},
			expected:    false,
			description: "Should not identify task with wrong frequency",
		},
		{
			name: "MissingConditions",
			task: &model.ActivityTask{
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIdentifier("ACCUMULATED_MEME_TRADING_1000"); return &id }(),
				ActionTarget:   func() *string { s := "memeTrade"; return &s }(),
				Frequency:      model.FrequencyProgressive,
				Conditions:     nil,
			},
			expected:    false,
			description: "Should not identify task without conditions",
		},
		{
			name: "RegularDailyTask",
			task: &model.ActivityTask{
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDDailyCheckin; return &id }(),
				ActionTarget:   func() *string { s := "checkin"; return &s }(),
				Frequency:      model.FrequencyDaily,
			},
			expected:    false,
			description: "Should not identify regular daily task",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := service.IsAccumulatedTradingVolumeTask(tc.task)
			assert.Equal(t, tc.expected, result, tc.description)
		})
	}
}
